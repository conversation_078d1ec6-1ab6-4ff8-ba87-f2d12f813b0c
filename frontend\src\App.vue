<script>
import Sidebar from './components/Sidebar.vue';
import ThemeToggle from './components/ThemeToggle.vue';

export default {
  name: 'App',
  components: { Sidebar, ThemeToggle },
  data() {
    return {
      sidebarCollapsed: false,
      isDark: false,
    };
  },
  created() {
    // 读取本地主题
    const theme = localStorage.getItem('theme');
    if (theme === 'dark') {
      this.isDark = true;
      document.documentElement.classList.add('dark');
    }
  },
  methods: {
    /**
     * 切换暗色/亮色模式
     * @param {boolean} dark 是否暗色
     */
    toggleTheme(dark) {
      this.isDark = dark;
      if (dark) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      }
    },
  },
};
</script>

<template>
  <div :class="['app-container', isDark ? 'dark' : 'light']">
    <header class="app-header">
      <div class="system-title">智能客服质检系统</div>
      <ThemeToggle :is-dark="isDark" @toggle-theme="toggleTheme" />
    </header>
    <div class="app-body">
      <Sidebar :collapsed="sidebarCollapsed" :is-dark="isDark" />
      <main class="app-main">
        <router-view />
      </main>
    </div>
    <button class="sidebar-toggle" :style="{ left: sidebarCollapsed ? '64px' : '200px' }" @click="sidebarCollapsed = !sidebarCollapsed">
      <span v-if="sidebarCollapsed">▶</span>
      <span v-else>◀</span>
    </button>
  </div>
</template>

<style scoped>
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.app-header {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  padding: 0 24px;
}
.system-title {
  font-size: 20px;
  font-weight: bold;
}
.app-body {
  flex: 1;
  display: flex;
  background: var(--el-bg-color-page);
}
.app-main {
  flex: 1;
  padding: 24px;
  overflow: auto;
}
.sidebar-toggle {
  position: absolute;
  top: 64px;
  left: 200px;
  z-index: 10;
  background: var(--el-bg-color);
  border: none;
  cursor: pointer;
  width: 24px;
  height: 40px;
  border-radius: 0 4px 4px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: left 0.2s;
}
.app-container .dark {
  background: #181818;
}
</style>
