<script>
import Sidebar from './components/Sidebar.vue';
import ThemeToggle from './components/ThemeToggle.vue';
import { <PERSON>, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

export default {
  name: 'App',
  components: {
    Sidebar,
    ThemeToggle,
    Bell,
    ArrowLeft,
    ArrowRight
  },
  data() {
    return {
      sidebarCollapsed: false,
      isDark: false,
    };
  },
  created() {
    // 读取本地主题
    const theme = localStorage.getItem('theme');
    if (theme === 'dark') {
      this.isDark = true;
      document.documentElement.classList.add('dark');
    }
  },
  methods: {
    /**
     * 切换暗色/亮色模式
     * @param {boolean} dark 是否暗色
     */
    toggleTheme(dark) {
      this.isDark = dark;
      if (dark) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      }
    },
  },
};
</script>

<template>
  <div :class="['app-container', isDark ? 'dark' : 'light']">
    <!-- 美化的头部导航栏 -->
    <header class="app-header">
      <div class="header-left">
        <div class="system-logo">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="system-title gradient-text">智能客服质检系统</div>
        </div>
      </div>

      <div class="header-right">
        <div class="header-actions">
          <!-- 通知图标 -->
          <div class="action-item">
            <el-badge :value="3" class="notification-badge">
              <el-button circle size="large" class="action-btn">
                <el-icon><Bell /></el-icon>
              </el-button>
            </el-badge>
          </div>

          <!-- 用户头像 -->
          <div class="action-item">
            <el-dropdown trigger="click">
              <div class="user-avatar">
                <img src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" alt="用户头像" />
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>个人设置</el-dropdown-item>
                  <el-dropdown-item>修改密码</el-dropdown-item>
                  <el-dropdown-item divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <!-- 主题切换 -->
          <div class="action-item">
            <ThemeToggle :is-dark="isDark" @toggle-theme="toggleTheme" />
          </div>
        </div>
      </div>
    </header>

    <!-- 主体内容区域 -->
    <div class="app-body">
      <Sidebar :collapsed="sidebarCollapsed" :is-dark="isDark" />
      <main class="app-main">
        <div class="main-content">
          <router-view />
        </div>
      </main>
    </div>

    <!-- 美化的侧边栏切换按钮 -->
    <button
      class="sidebar-toggle hover-lift"
      :class="{ collapsed: sidebarCollapsed }"
      :style="{ left: sidebarCollapsed ? '64px' : '200px' }"
      @click="sidebarCollapsed = !sidebarCollapsed"
    >
      <el-icon class="toggle-icon">
        <ArrowLeft v-if="!sidebarCollapsed" />
        <ArrowRight v-if="sidebarCollapsed" />
      </el-icon>
    </button>
  </div>
</template>

<style scoped>
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
  transition: background-color var(--transition-normal);
}

/* 头部导航栏样式 */
.app-header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 0 24px;
  box-shadow: var(--shadow-sm);
  position: relative;
  z-index: 100;
}

.app-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
}

.header-left {
  display: flex;
  align-items: center;
}

.system-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-primary);
  border-radius: var(--radius-md);
  color: white;
  box-shadow: var(--shadow-md);
}

.logo-icon svg {
  width: 20px;
  height: 20px;
}

.system-title {
  font-size: 22px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-item {
  display: flex;
  align-items: center;
}

.action-btn {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-secondary) !important;
  transition: all var(--transition-fast) !important;
}

.action-btn:hover {
  background: var(--bg-tertiary) !important;
  color: var(--primary-color) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md) !important;
}

.notification-badge :deep(.el-badge__content) {
  background: var(--gradient-secondary);
  border: none;
  font-size: 10px;
  font-weight: 600;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid var(--border-color);
  transition: all var(--transition-fast);
}

.user-avatar:hover {
  border-color: var(--primary-color);
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 主体内容区域 */
.app-body {
  flex: 1;
  display: flex;
  background: var(--bg-secondary);
  overflow: hidden;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-content {
  flex: 1;
  padding: 24px;
  overflow: auto;
  background: var(--bg-secondary);
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
  position: fixed;
  top: 80px;
  left: 200px;
  z-index: 1000;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  color: var(--text-secondary);
}

.sidebar-toggle:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px) scale(1.1);
  box-shadow: var(--shadow-xl);
}

.sidebar-toggle.collapsed {
  left: 64px;
}

.toggle-icon {
  font-size: 14px;
  transition: transform var(--transition-fast);
}

.sidebar-toggle:hover .toggle-icon {
  transform: scale(1.2);
}

/* 暗色模式适配 */
.dark .app-header {
  background: var(--bg-dark-primary);
  border-bottom-color: var(--border-dark);
}

.dark .action-btn {
  background: var(--bg-dark-secondary) !important;
  border-color: var(--border-dark) !important;
  color: var(--text-dark-secondary) !important;
}

.dark .action-btn:hover {
  background: var(--bg-dark-tertiary) !important;
  color: var(--primary-light) !important;
}

.dark .sidebar-toggle {
  background: var(--bg-dark-primary);
  border-color: var(--border-dark);
  color: var(--text-dark-secondary);
}

.dark .user-avatar {
  border-color: var(--border-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
  }

  .system-title {
    font-size: 18px;
  }

  .header-actions {
    gap: 12px;
  }

  .main-content {
    padding: 16px;
  }

  .sidebar-toggle {
    width: 28px;
    height: 28px;
  }
}
</style>
