# 4.4. 复核与申诉

## 4.4.1. 复核策略管理

### 功能目标
* **为管理者提供灵活的配置中心，定义AI自动质检后需人工复核的条件。**
* **实现精准筛选，将人力聚焦于最需关注的通话。**
* **支持复核任务分配规则，实现自动化、均衡化分发。**
* **提升质检效率和针对性，确保高风险或AI不准的通话得到人工校验。**

### 目标用户
* 主要用户：质检主管。

### 核心功能与界面描述
* **复核策略列表页**
  * 页面布局：标准"头部+筛选器+列表+分页"
  * 页面元素：
    * 页面标题："复核策略管理"
    * 副标题："管理质检复核策略，配置触发条件和分配规则"
    * 核心操作按钮：
      * [主要] 新建策略：抽屉弹出创建表单
    * 搜索筛选器：
      * 策略名称、状态
    * 策略列表（Table）：
      * 列定义：
        * 序号
        * 策略名称+状态徽章，下方简要描述
        * 触发条件：如"AI初检得分<60"，复杂条件可悬浮查看
        * 分配规则：如"轮询分配给张三、李四"
        * 创建人/创建时间
        * 操作：
          * [编辑]
          * [启用/禁用]
          * [删除]
* **创建/编辑策略抽屉表单**
  * 第一部分：基本信息
    * 策略名称、策略描述
  * 第二部分：触发条件 (IF)
    * 逻辑关系：AND/OR
    * 条件构建器：动态增删，每个条件为[对象]+[操作符]+[值]
      * 对象如AI初检得分、命中规则、规则类型、重要程度、通话时长、坐席、班组、客户号码等
      * 操作符如>、<、=、是、不是、属于、不属于等
      * 值为输入框、下拉、滑块等
  * 第三部分：执行分配 (THEN)
    * 分配模式：
      * 轮询分配
      * 平均分配
      * 指定分配
    * 分配目标：多选/单选复核员
  * 抽屉底部操作栏：保存、取消

### 业务规则
* 策略名称必须唯一
* 可同时启用多个策略，需有冲突处理逻辑
* 一通电话被触发进入复核后应锁定，避免重复触发

### 与其他模块的关联
* AI自动质检流程：本模块上游，AI结果送入策略引擎
* 我的复核任务：本模块下游，策略触发后为指定复核员生成待办

### 设计价值
* **自动化工作流**：AI质检与人工复核无缝连接，实现自动分发
* **精确筛选**：灵活条件构建器聚焦高风险场景
* **负载均衡**：分配模式支持轮询/平均，保证公平高效

## 4.4.2. 我的复核任务

### 功能目标
* **为复核员提供清晰高效的个人核心工作界面，集中管理所有复核任务。**
* **取代首页"快速待办"，提供完整、可筛选、可追溯的任务中心。**
* **直观展示待办与已完成任务，便于规划与回顾。**
* **强大筛选功能，快速定位特定任务。**

### 目标用户
* 主要用户：复核员。

### 核心功能与界面描述
* **页面布局与导航**
  * 标准"头部+Tab切换+筛选器+列表+分页"
  * 页面标题："我的复核任务"
  * 副标题："管理和处理分配给我的质检复核任务"
  * 页面图标：ClipboardCheck
  * 页面徽章：动态显示"待处理X条"
* **Tab切换（待处理/已完成）**
  * 待处理Tab：显示所有"待处理"任务，Tab角标显示数量
  * 已完成Tab：显示所有已处理任务，Tab角标显示数量
* **统一搜索筛选器**
  * 通用字段：记录编号、所属任务、被检坐席、班组、AI初检得分范围、分配时间范围、质检结果
  * "已完成"专属字段：最终得分范围
  * 支持常用条件默认展开，更多条件可收起
  * "查询""重置"按钮
* **任务表格 (Table)**
  * "待处理"列定义：
    * 序号
    * 记录编号
    * 所属任务
    * 被检坐席
    * 所属班组
    * 通话时长
    * 触发复核原因
    * AI初检得分（分数段颜色区分）
    * 分配时间
    * 操作："处理"按钮
  * "已完成"列定义：
    * 包含"待处理"大部分列，新增/替换：
    * 最终得分
    * 质检结果
    * 处理时间
    * 操作："查看"按钮
  * 表格高亮关键信息，支持横向滚动，空状态友好提示
* **分页组件**：表格下方，支持大量记录浏览

### 核心交互与价值
* 任务处理流程：查看"待处理"->选择任务->"处理"->跳转详情页->完成后自动移至"已完成"
* 信息查询与追溯：切换"已完成"并筛选，便于回顾历史
* 批量操作（潜在扩展）：可扩展批量分配、退回等

## 4.4.3. 申诉处理

### 功能目标
* **为质检主管/申诉处理人提供集中处理所有坐席申诉的平台。**
* **确保每条申诉及时响应、公正处理、可追溯，保障坐席权益。**
* **结构化管理申诉流程，清晰分离待办与已办。**
* **通过申诉数据管理，发现规则或AI判断问题。**

### 目标用户
* 主要用户：质检主管、申诉处理人。

### 核心功能与界面描述
* **页面布局与导航**
  * 与"我的复核任务"结构一致，"头部+Tab切换+筛选器+列表+分页"
  * 页面标题："申诉处理"
  * 副标题："处理坐席对质检结果提出的申诉"
  * 页面图标：MessageSquareX
* **Tab切换（待处理/已完成）**
  * 待处理Tab：显示所有未处理申诉，Tab角标显示数量
  * 已完成Tab：显示所有已处理申诉
* **统一搜索筛选器**
  * 通用字段：记录编号、所属任务、申请坐席、班组、AI初检得分范围、申请时间范围、质检结果
  * "已完成"专属字段：申诉结果、处理时间范围、最终得分范围
* **申诉任务表格 (Table)**
  * "待处理"列定义：
    * 序号、记录编号、所属任务
    * 申诉坐席
    * 所属班组
    * 通话时长
    * AI初检得分
    * 人工复核得分
    * 质检结果
    * 申请理由
    * 申请时间
    * 操作："处理"按钮
  * "已完成"列定义：
    * 包含"待处理"大部分列，新增/替换：
    * 最终得分
    * 申诉结果
    * 处理时间
    * 操作："查看"按钮
  * 信息密度高，状态清晰，关键字段高亮
* **分页组件**：表格下方，支持大量记录浏览

### 核心交互与价值
* 申诉处理流程：
  1. 查看"待处理"->选择申诉->"处理"
  2. 跳转详情页，切换申诉处理模式
  3. 结合录音、文本、AI结果、坐席理由，做出决定并填写意见
  4. 完成后自动移至"已完成"
* 公正性与可追溯性：所有处理有据可查，流程透明
* 管理洞察：通过筛选申诉记录发现沟通或规则问题，为申诉洞察报告提供数据

## 4.4.4. 多模式会话详情页

### 功能目标
* **为不同角色用户提供沉浸式全景视图和操作平台，支持复核/申诉/只读等多模式。**
* **连接宏观数据与微观场景，成为所有质检判断和操作的最终场所。**
* **音频、文本、评分规则三者无缝联动，极大提升复听、定位和分析效率。**

### 目标用户
* 所有核心角色：质检主管、班组长、复核员、客服坐席。

### 核心功能与界面描述（三栏式布局）
* 任务信息：显示所属任务、质检方案、合格线等
* 通话信息：被检坐席、班组、客户号码、通话时间、时长
* 分数演进面板：可视化分数生命周期（AI初检→人工复核→申诉裁定），显示最终得分和结论
* 复核触发原因：复核模式下显示，如"低分触发"
* 处理时间轴：只读模式下显示，展示全流程关键节点和操作人
* 操作面板：根据viewMode渲染不同操作组件
  * 复核模式：ReviewPanel，输入意见、调整分数、提交
  * 申诉处理模式：AppealPanel，查看理由，决定同意/驳回并填写意见
  * 坐席申诉模式：InitiateAppealPanel，填写理由发起申诉
  * 只读模式：BasicViewPanel，提示只读
* 音频播放器：标准控件，支持音文同步、跳转
* 通话文本：按角色和时间顺序对话展示，支持高亮、事件标注、点击跳转
* 评分详情：列表展示所有规则及打分结果，命中状态、得分、交互跳转，复核模式下可修改
* 复核/申诉详情：只读模式下展示操作人、时间、意见/理由

### 核心交互与价值
* **三位一体联动**：音频、文本、评分规则无缝联动，极致效率和体验
* **模式化交互**：viewMode参数精准控制界面和权限，避免混淆
* **从宏观到微观的闭环**：所有分析和列表页面的最终落脚点，构成完整的分析-处理闭环
