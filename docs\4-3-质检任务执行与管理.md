# 4.3. 质检任务执行与管理

## 4.3.1. 质检计划管理

### 功能目标
* **为管理者提供一个配置自动化、周期性质检任务的中心。**
* **实现“一次配置，永久（或周期性）运行”的自动化能力，将管理者从重复性的任务创建工作中解放出来。**
* **清晰地定义自动化任务的执行周期、质检范围和所用规则，确保自动化流程的精确可控。**
* **提供对自动化计划的生命周期管理和执行历史的追溯能力。**

### 目标用户
* 主要用户：质检主管。

### 核心功能与界面描述
* **质检计划列表页**
  * 页面布局：标准“头部+筛选器+列表+分页”
  * 页面元素：
    * 页面标题：“质检计划管理”
    * 核心操作按钮：
      * [主要] 新建计划：抽屉弹出创建表单
    * 搜索筛选器：
      * 计划名称、质检方案、创建人、创建时间
    * Tab 切换：按计划生命周期状态分类
      * 全部
      * 活动中：正在运行
      * 已暂停：手动暂停
      * 已过期：到期不再运行
    * 计划列表（Table）：
      * 列定义：
        * 序号
        * 计划名称及简要描述
        * 计划状态：不同颜色徽章
        * 执行周期：如“每天 02:00”、“每周一 09:00”
        * 上次运行时间
        * 上次运行状态：成功/失败
        * 质检方案
        * 操作：
          * [编辑]
          * [启动/暂停]
          * [查看执行历史]
          * [删除]
* **创建/编辑计划抽屉表单**
  * 第一部分：基本信息
    * 计划名称、计划描述
  * 第二部分：执行计划 (WHEN)
    * 重复频率：每天/每周/每月
    * 执行时间：具体触发时间点
    * 结束于：可选失效日期
  * 第三部分：质检范围 (WHO & WHAT)
    * 数据来源：选择数据源
    * 通话时间范围：动态、相对时间，如“过去24小时”、“上一个自然日”等
    * 质检对象：全部坐席或按组织架构选择
  * 第四部分：质检规则 (HOW)
    * 质检方案：选择已启用方案
    * 质检模式：
      * 全量质检
      * 抽样质检（需设置抽样比例）
  * 抽屉底部操作栏：保存、取消

### 业务规则
* 计划名称必须唯一
* “通话时间范围”的动态计算是自动化关键
* 每次计划执行都会在“质检任务管理”页面生成一条新任务记录

### 与其他模块的关联
* 质检方案配置：本模块的配置来源
* 质检任务管理：本模块的产出，“查看执行历史”形成追溯链

### 设计价值
* **自动化**：一次配置，自动运行，解放管理者，保证持续性和一致性
* **追溯性**：计划与任务关联，流程透明、可审计
* **灵活性**：支持全量和抽样两种模式，满足不同场景需求

## 4.3.2. 质检任务管理

### 功能目标
* **为管理者提供所有具体质检批次的管理中心，无论自动生成还是手动创建。**
* **支持根据临时需求创建一次性手动质检任务。**
* **提供每个质检任务执行状态的实时监控，包括进度、成功或失败。**
* **作为所有质检任务的统一入口，允许下钻到任何任务的详细结果。**

### 目标用户
* 主要用户：质检主管。

### 核心功能与界面描述
* **质检任务列表页**
  * 页面布局：与计划管理类似，“头部+筛选器+列表+分页”
  * 页面元素：
    * 页面标题：“质检任务管理”
    * 核心操作按钮：
      * [主要] 新建任务：抽屉弹出创建表单
    * 搜索筛选器：
      * 任务名称、来源、质检方案、创建人、创建时间
    * Tab 切换：按任务执行状态分类
      * 全部
      * 待执行
      * 执行中
      * 已完成
      * 执行失败
    * 任务列表（Table）：
      * 列定义：
        * 序号
        * 任务名称
        * 任务状态：不同颜色徽章
        * 任务进度：进度条+数字
        * 质检方案
        * 来源：手动/计划生成，计划生成可跳转源计划
        * 创建人/创建时间
        * 操作：
          * [查看详情]
          * [编辑]（仅待执行）
          * [删除]（非执行中）
* **创建/编辑任务抽屉表单**
  * 第一部分：基本信息
    * 任务名称、任务描述
  * 第二部分：质检范围
    * 数据来源
    * 通话时间范围：固定、绝对时间范围（如2023-11-01 00:00:00~2023-11-10 23:59:59）
    * 质检对象
  * 第三部分：质检规则与模式
    * 选择质检方案和质检模式（全量/抽样）
  * 第四部分：执行计划
    * 执行方式：
      * 立即执行
      * 定时执行（设置未来时间点）
  * 抽屉底部操作栏：保存、取消

### 业务规则
* 任务名称必须唯一
* 任务进入“执行中”后配置不可编辑
* “任务进度”需后台实时查询接口

### 与其他模块的关联
* 质检计划管理：本模块中“由计划生成”任务的来源
* 质检任务详情页：本模块的直接下游，“查看详情”连接批次与明细

### 设计价值
* **手动与一次性**：满足一次性、临时质检需求，与计划管理互补
* **任务与明细关联**：宏观任务与微观结果下钻，批次到个体清晰
* **进度监控**：进度条实时监控大型任务执行情况
* **统一视图**：所有任务的“户口本”和“成长记录”，统一可追溯

## 4.3.3. 质检任务详情页（结果列表）

### 功能目标
* **为管理者提供清晰界面，查看特定质检任务（批次）的执行结果汇总和所有通话记录明细。**
* **作为连接“宏观任务”与“微观记录”的桥梁，实现无缝下钻。**
* **支持任务结果内部二次筛选和查询，帮助快速定位问题。**
* **直观展示任务整体产出，如平均分、合格率等。**

### 目标用户
* 主要用户：质检主管、班组长。

### 核心功能与界面描述
* **页面布局与导航**
  * 页面布局：顶部任务摘要，下方筛选器和结果列表
  * 导航：
    * 左上角返回按钮，返回任务管理列表
    * 页面标题显示当前任务名称
    * 标题下方显示任务唯一ID
* **任务信息总览卡片**
  * 顶部卡片展示任务核心配置信息：
    * 质检方案
    * 质检范围
    * 通话时段
    * 创建人/创建时间
    * 完成时间
* **任务统计数据卡片 (Statistics Cards)**
  * KPI卡片展示核心产出：
    * 任务记录总数
    * 任务平均分
    * 合格率
    * 进入人工复核数量
* **统一搜索筛选器**
  * 在当前任务结果内部二次筛选和查询
  * 筛选字段：如记录编号、坐席、客户号码、得分范围、质检结果、申诉状态等
* **质检明细表格 (Table)**
  * 表格展示所有通话记录详细质检结果
  * 列定义：
    * 序号
    * 记录编号（可点击跳转详情）
    * 坐席、所属班组
    * 客户号码（脱敏）
    * 通话开始时间、通话时长
    * 最终得分（可悬浮展示分数演进）
    * 质检结果（合格/不合格徽章）
    * 申诉信息（状态、结果、时间）
    * 操作（查看详情）

### 业务规则
* 此页面为只读，主要用于数据展示和下钻
* 筛选器所有条件为AND关系，便于精确查询
* 表格数据支持按各列排序

### 核心交互与价值
* **从宏观到微观的逐层下钻**：
  1. 用户从[质检任务管理]页面点击宏观任务项
  2. 进入本页面，看到中观统计数据（KPI卡片）
  3. 在表格中下钻到任意微观记录详情
* **任务成果评估**：本页是复盘任务成败的核心依据，管理者可快速看到整体效果并查看具体记录
* **内部问题定位**：通过筛选快速定位问题，如筛出所有“不合格”记录集中分析和辅导