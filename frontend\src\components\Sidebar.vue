<template>
  <div :class="['sidebar-menu', { collapsed }]">
    <el-menu
      :default-active="activeMenu"
      :collapse="collapsed"
      :background-color="isDark ? '#181818' : '#fff'"
      :text-color="isDark ? '#fff' : '#303133'"
      :active-text-color="'#409EFF'"
      unique-opened
      router
    >
      <!-- 直接菜单项（没有子菜单） -->
      <el-menu-item
        v-for="group in menuGroups.filter(g => g.isDirectMenu)"
        :key="group.index"
        :index="group.index"
      >
        <span class="sidebar-icon">
          <el-icon>
            <component :is="getIconComponent(group.icon)" />
          </el-icon>
        </span>
        {{ group.title }}
      </el-menu-item>

      <!-- 有子菜单的菜单组 -->
      <el-sub-menu
        v-for="group in menuGroups.filter(g => !g.isDirectMenu)"
        :key="group.index"
        :index="group.index"
      >
        <template #title>
          <span class="sidebar-icon">
            <el-icon>
              <component :is="getIconComponent(group.icon)" />
            </el-icon>
          </span>
          <span>{{ group.title }}</span>
        </template>
        <el-menu-item v-for="item in group.children" :key="item.index" :index="item.index">
          <span class="sidebar-icon">
            <el-icon>
              <component :is="getIconComponent(item.icon)" />
            </el-icon>
          </span>
          {{ item.title }}
        </el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script>
import {
  Menu as MenuIcon,
  Setting as SettingIcon,
  Monitor as MonitorIcon,
  DataAnalysis as DataAnalysisIcon,
  Tools as ToolsIcon,
  User as UserIcon,
  UserFilled,
  Bell as BellIcon,
  Document as DocumentIcon,
  Edit as EditIcon,
  Folder as FolderIcon,
  MessageBox as MessageBoxIcon,
  Collection as CollectionIcon,
  Tickets as TicketsIcon,
  Notification as NotificationIcon,
  Histogram as HistogramIcon,
  TrendCharts as TrendChartsIcon,
  Search as SearchIcon,
  Connection as ConnectionIcon,
  Cpu as CpuIcon,
  BellFilled,
  HomeFilled as HomeIcon,
} from '@element-plus/icons-vue';

/**
 * 侧边栏导航菜单组件
 * @module Sidebar
 */
export default {
  name: 'Sidebar',
  components: {
    MenuIcon,
    SettingIcon,
    MonitorIcon,
    DataAnalysisIcon,
    ToolsIcon,
    UserIcon,
    UserFilled,
    BellIcon,
    DocumentIcon,
    EditIcon,
    FolderIcon,
    MessageBoxIcon,
    CollectionIcon,
    TicketsIcon,
    NotificationIcon,
    HistogramIcon,
    TrendChartsIcon,
    SearchIcon,
    ConnectionIcon,
    CpuIcon,
    BellFilled,
    HomeIcon,
  },
  props: {
    collapsed: {
      type: Boolean,
      default: false,
    },
    isDark: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeMenu: '',
      /**
       * 图标映射对象
       */
      iconMap: {
        MenuIcon,
        SettingIcon,
        MonitorIcon,
        DataAnalysisIcon,
        ToolsIcon,
        UserIcon,
        UserFilled,
        BellIcon,
        DocumentIcon,
        EditIcon,
        FolderIcon,
        MessageBoxIcon,
        CollectionIcon,
        TicketsIcon,
        NotificationIcon,
        HistogramIcon,
        TrendChartsIcon,
        SearchIcon,
        ConnectionIcon,
        CpuIcon,
        BellFilled,
        HomeIcon,
      },
      /**
       * 菜单分组与项，严格按产品架构，并分配合适图标
       */
      menuGroups: [
        {
          index: '0',
          title: '主页',
          icon: 'HomeIcon',
          isDirectMenu: true, // 标记为直接菜单项，没有子菜单
        },
        {
          index: '1',
          title: '概览与工作台',
          icon: 'MenuIcon',
          children: [
            { index: '1-1', title: '主管首页', icon: 'UserIcon' },
            { index: '1-2', title: '班组长首页', icon: 'UserFilled' },
            { index: '1-3', title: '复核员首页', icon: 'UserFilled' },
            { index: '1-4', title: '客服坐席首页', icon: 'UserFilled' },
            { index: '1-5', title: '我的复核任务', icon: 'EditIcon' },
            { index: '1-6', title: '申诉处理', icon: 'MessageBoxIcon' },
            { index: '1-7', title: '质检成绩页', icon: 'HistogramIcon' },
            { index: '1-8', title: '通知中心', icon: 'BellIcon' },
          ],
        },
        {
          index: '2',
          title: '质检管理',
          icon: 'SettingIcon',
          children: [
            { index: '2-1', title: '词库管理', icon: 'CollectionIcon' },
            { index: '2-2', title: '规则库管理', icon: 'FolderIcon' },
            { index: '2-3', title: '质检方案配置', icon: 'DocumentIcon' },
            { index: '2-4', title: '质检计划管理', icon: 'EditIcon' },
            { index: '2-5', title: '质检任务管理', icon: 'TicketsIcon' },
            { index: '2-6', title: '复核策略管理', icon: 'ToolsIcon' },
          ],
        },
        {
          index: '3',
          title: '实时监控与干预',
          icon: 'MonitorIcon',
          children: [
            { index: '3-1', title: '实时预警中心', icon: 'BellFilled' },
            { index: '3-2', title: '预警跟进中心', icon: 'NotificationIcon' },
          ],
        },
        {
          index: '4',
          title: '数据洞察与分析',
          icon: 'DataAnalysisIcon',
          children: [
            { index: '4-1', title: '质检运营总览', icon: 'HistogramIcon' },
            { index: '4-2', title: '服务质量深度分析', icon: 'TrendChartsIcon' },
            { index: '4-3', title: '复核工作分析报告', icon: 'DocumentIcon' },
            { index: '4-4', title: '坐席申诉洞察报告', icon: 'DocumentIcon' },
            { index: '4-5', title: '质检明细查询', icon: 'SearchIcon' },
            { index: '4-6', title: '历史预警查询', icon: 'SearchIcon' },
          ],
        },
        {
          index: '5',
          title: '系统管理',
          icon: 'ToolsIcon',
          children: [
            { index: '5-1', title: '数据源管理', icon: 'ConnectionIcon' },
            { index: '5-2', title: '语音识别引擎管理', icon: 'CpuIcon' },
            { index: '5-3', title: '大语言模型管理', icon: 'CpuIcon' },
            { index: '5-4', title: '通知渠道管理', icon: 'NotificationIcon' },
          ],
        },
      ],
    };
  },
  methods: {
    /**
     * 获取图标组件
     * @param {string} iconName 图标名称
     * @returns {Object} 图标组件
     */
    getIconComponent(iconName) {
      return this.iconMap[iconName] || this.iconMap.MenuIcon;
    },
  },
};
</script>

<style scoped>
.sidebar-menu {
  width: 200px;
  min-height: 100vh;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.sidebar-menu::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, transparent 0%, var(--primary-color) 50%, transparent 100%);
  opacity: 0.3;
}

.sidebar-menu.collapsed {
  width: 64px;
}

.sidebar-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 18px;
  margin-right: 8px;
  transition: all var(--transition-fast);
}

.sidebar-icon svg {
  width: 18px !important;
  height: 18px !important;
  font-size: 18px !important;
  transition: all var(--transition-fast);
}

/* Element Plus 菜单样式覆盖 */
:deep(.el-menu) {
  border-right: none !important;
  background: transparent !important;
}

:deep(.el-sub-menu) {
  margin: 4px 8px;
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: background-color var(--transition-fast), transform var(--transition-fast);
  will-change: transform, background-color;
}

:deep(.el-sub-menu:hover) {
  background: var(--bg-secondary);
  transform: translateX(2px);
}

:deep(.el-sub-menu__title) {
  height: 48px !important;
  line-height: 48px !important;
  padding: 0 16px !important;
  font-weight: 600;
  font-size: 14px;
  border-radius: var(--radius-md);
  transition: color var(--transition-fast), transform var(--transition-fast);
  position: relative;
  overflow: hidden;
  will-change: transform, color;
}

:deep(.el-sub-menu__title::before) {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  background: var(--gradient-primary);
  transition: width 0.2s ease-out;
  z-index: -1;
  will-change: width;
}

:deep(.el-sub-menu__title:hover::before) {
  width: 100%;
}

:deep(.el-sub-menu__title:hover) {
  color: white !important;
  transform: translateX(4px);
}

:deep(.el-sub-menu__title:hover .sidebar-icon) {
  color: white !important;
  transform: scale(1.1);
}

:deep(.el-sub-menu.is-opened .el-sub-menu__title) {
  background: var(--bg-tertiary);
  color: var(--primary-color) !important;
}

:deep(.el-menu-item) {
  height: 40px !important;
  line-height: 40px !important;
  margin: 2px 12px 2px 24px !important;
  padding: 0 12px !important;
  border-radius: var(--radius-sm);
  font-size: 13px;
  transition: background-color var(--transition-fast), color var(--transition-fast), transform var(--transition-fast);
  position: relative;
  overflow: hidden;
  will-change: transform, background-color, color;
}

:deep(.el-menu-item::before) {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 0;
  background: var(--primary-color);
  transition: height var(--transition-fast);
  border-radius: 0 2px 2px 0;
}

:deep(.el-menu-item:hover::before) {
  height: 100%;
}

:deep(.el-menu-item:hover) {
  background: var(--bg-secondary) !important;
  color: var(--primary-color) !important;
  transform: translateX(4px);
}

:deep(.el-menu-item:hover .sidebar-icon) {
  color: var(--primary-color) !important;
  transform: scale(1.1);
}

:deep(.el-menu-item.is-active) {
  background: var(--bg-tertiary) !important;
  color: var(--primary-color) !important;
  font-weight: 600;
}

:deep(.el-menu-item.is-active::before) {
  height: 100%;
}

:deep(.el-menu-item.is-active .sidebar-icon) {
  color: var(--primary-color) !important;
}

/* 折叠状态样式 */
.sidebar-menu.collapsed :deep(.el-sub-menu) {
  margin: 4px 8px;
}

/* 折叠状态下的直接菜单项样式 */
.sidebar-menu.collapsed :deep(.el-menu-item:not(.el-menu-item--child)) {
  margin: 4px 8px !important;
  padding: 0 !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
  width: calc(100% - 16px) !important;
}

.sidebar-menu.collapsed :deep(.el-menu-item:not(.el-menu-item--child) .sidebar-icon) {
  margin-right: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
}

.sidebar-menu.collapsed :deep(.el-menu-item:not(.el-menu-item--child) span:not(.sidebar-icon)) {
  display: none !important;
}

.sidebar-menu.collapsed :deep(.el-sub-menu__title) {
  padding: 0 !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
}

.sidebar-menu.collapsed :deep(.el-sub-menu__title .sidebar-icon) {
  margin-right: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
}

.sidebar-menu.collapsed :deep(.el-sub-menu__title .sidebar-icon svg) {
  width: 20px !important;
  height: 20px !important;
  display: block !important;
  color: inherit !important;
}

.sidebar-menu.collapsed :deep(.el-sub-menu__title span:not(.sidebar-icon)) {
  display: none !important;
}

.sidebar-menu.collapsed :deep(.el-sub-menu__title .el-sub-menu__icon-arrow) {
  display: none !important;
}

.sidebar-menu.collapsed :deep(.el-menu-item) {
  margin: 2px 8px !important;
  padding: 0 !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
  width: calc(100% - 16px) !important;
}

.sidebar-menu.collapsed :deep(.el-menu-item .sidebar-icon) {
  margin-right: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
}

.sidebar-menu.collapsed :deep(.el-menu-item .sidebar-icon svg) {
  width: 20px !important;
  height: 20px !important;
  display: block !important;
  color: inherit !important;
}

.sidebar-menu.collapsed :deep(.el-menu-item span:not(.sidebar-icon)) {
  display: none !important;
}

/* 确保折叠状态下图标容器正确显示 */
.sidebar-menu.collapsed :deep(.sidebar-icon) {
  margin-right: 0 !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

/* 确保Element Plus图标组件正确显示 */
.sidebar-menu.collapsed :deep(.sidebar-icon .el-icon) {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.sidebar-menu.collapsed :deep(.sidebar-icon .el-icon svg) {
  width: 18px !important;
  height: 18px !important;
  display: block !important;
}

/* 强制显示所有图标元素 */
.sidebar-menu.collapsed :deep(.sidebar-icon *) {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 额外的图标显示保证 */
.sidebar-menu.collapsed :deep(.el-sub-menu__title),
.sidebar-menu.collapsed :deep(.el-menu-item) {
  overflow: visible !important;
}

.sidebar-menu.collapsed :deep(.el-sub-menu__title .sidebar-icon),
.sidebar-menu.collapsed :deep(.el-menu-item .sidebar-icon) {
  position: relative !important;
  z-index: 10 !important;
}

/* 确保折叠状态下的菜单项高度 */
.sidebar-menu.collapsed :deep(.el-sub-menu__title) {
  min-height: 48px !important;
}

.sidebar-menu.collapsed :deep(.el-menu-item) {
  min-height: 40px !important;
}

/* 暗色模式适配 */
.dark .sidebar-menu {
  background: var(--bg-dark-primary);
  border-right-color: var(--border-dark);
}

.dark :deep(.el-sub-menu:hover) {
  background: var(--bg-dark-secondary);
}

.dark :deep(.el-sub-menu.is-opened .el-sub-menu__title) {
  background: var(--bg-dark-tertiary);
  color: var(--primary-light) !important;
}

.dark :deep(.el-menu-item:hover) {
  background: var(--bg-dark-secondary) !important;
  color: var(--primary-light) !important;
}

.dark :deep(.el-menu-item:hover .sidebar-icon) {
  color: var(--primary-light) !important;
}

.dark :deep(.el-menu-item.is-active) {
  background: var(--bg-dark-tertiary) !important;
  color: var(--primary-light) !important;
}

.dark :deep(.el-menu-item.is-active .sidebar-icon) {
  color: var(--primary-light) !important;
}

/* 滚动条样式 */
.sidebar-menu :deep(.el-menu) {
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-menu :deep(.el-menu)::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu :deep(.el-menu)::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu :deep(.el-menu)::-webkit-scrollbar-thumb {
  background: var(--text-tertiary);
  border-radius: 2px;
}

.sidebar-menu :deep(.el-menu)::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
</style>