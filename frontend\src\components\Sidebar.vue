<template>
  <div :class="['sidebar-menu', { collapsed }]">
    <el-menu
      :default-active="activeMenu"
      :collapse="collapsed"
      :background-color="isDark ? '#181818' : '#fff'"
      :text-color="isDark ? '#fff' : '#303133'"
      :active-text-color="'#409EFF'"
      unique-opened
      router
    >
      <el-sub-menu v-for="group in menuGroups" :key="group.index" :index="group.index">
        <template #title>
          <span class="sidebar-icon">
            <component :is="group.icon" />
          </span>
          <span>{{ group.title }}</span>
        </template>
        <el-menu-item v-for="item in group.children" :key="item.index" :index="item.index">
          <span class="sidebar-icon">
            <component :is="item.icon" />
          </span>
          {{ item.title }}
        </el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script>
import {
  Menu as MenuIcon,
  Setting as SettingIcon,
  Monitor as MonitorIcon,
  DataAnalysis as DataAnalysisIcon,
  Tools as ToolsIcon,
  User as UserIcon,
  UserFilled,
  Bell as BellIcon,
  Document as DocumentIcon,
  Edit as EditIcon,
  Folder as FolderIcon,
  MessageBox as MessageBoxIcon,
  Collection as CollectionIcon,
  Tickets as TicketsIcon,
  Notification as NotificationIcon,
  Histogram as HistogramIcon,
  TrendCharts as TrendChartsIcon,
  Search as SearchIcon,
  Connection as ConnectionIcon,
  Cpu as CpuIcon,
  BellFilled,
} from '@element-plus/icons-vue';

/**
 * 侧边栏导航菜单组件
 * @module Sidebar
 */
export default {
  name: 'Sidebar',
  components: {
    MenuIcon,
    SettingIcon,
    MonitorIcon,
    DataAnalysisIcon,
    ToolsIcon,
    UserIcon,
    UserFilled,
    BellIcon,
    DocumentIcon,
    EditIcon,
    FolderIcon,
    MessageBoxIcon,
    CollectionIcon,
    TicketsIcon,
    NotificationIcon,
    HistogramIcon,
    TrendChartsIcon,
    SearchIcon,
    ConnectionIcon,
    CpuIcon,
    BellFilled,
  },
  props: {
    collapsed: {
      type: Boolean,
      default: false,
    },
    isDark: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeMenu: '',
      /**
       * 菜单分组与项，严格按产品架构，并分配合适图标
       */
      menuGroups: [
        {
          index: '1',
          title: '概览与工作台',
          icon: MenuIcon,
          children: [
            { index: '1-1', title: '主管首页', icon: UserIcon },
            { index: '1-2', title: '班组长首页', icon: UserFilled },
            { index: '1-3', title: '复核员首页', icon: UserFilled },
            { index: '1-4', title: '客服坐席首页', icon: UserFilled },
            { index: '1-5', title: '我的复核任务', icon: EditIcon },
            { index: '1-6', title: '申诉处理', icon: MessageBoxIcon },
            { index: '1-7', title: '质检成绩页', icon: HistogramIcon },
            { index: '1-8', title: '通知中心', icon: BellIcon },
          ],
        },
        {
          index: '2',
          title: '质检管理',
          icon: SettingIcon,
          children: [
            { index: '2-1', title: '词库管理', icon: CollectionIcon },
            { index: '2-2', title: '规则库管理', icon: FolderIcon },
            { index: '2-3', title: '质检方案配置', icon: DocumentIcon },
            { index: '2-4', title: '质检计划管理', icon: EditIcon },
            { index: '2-5', title: '质检任务管理', icon: TicketsIcon },
            { index: '2-6', title: '复核策略管理', icon: ToolsIcon },
          ],
        },
        {
          index: '3',
          title: '实时监控与干预',
          icon: MonitorIcon,
          children: [
            { index: '3-1', title: '实时预警中心', icon: BellFilled },
            { index: '3-2', title: '预警跟进中心', icon: NotificationIcon },
          ],
        },
        {
          index: '4',
          title: '数据洞察与分析',
          icon: DataAnalysisIcon,
          children: [
            { index: '4-1', title: '质检运营总览', icon: HistogramIcon },
            { index: '4-2', title: '服务质量深度分析', icon: TrendChartsIcon },
            { index: '4-3', title: '复核工作分析报告', icon: DocumentIcon },
            { index: '4-4', title: '坐席申诉洞察报告', icon: DocumentIcon },
            { index: '4-5', title: '质检明细查询', icon: SearchIcon },
            { index: '4-6', title: '历史预警查询', icon: SearchIcon },
          ],
        },
        {
          index: '5',
          title: '系统管理',
          icon: ToolsIcon,
          children: [
            { index: '5-1', title: '数据源管理', icon: ConnectionIcon },
            { index: '5-2', title: '语音识别引擎管理', icon: CpuIcon },
            { index: '5-3', title: '大语言模型管理', icon: CpuIcon },
            { index: '5-4', title: '通知渠道管理', icon: NotificationIcon },
          ],
        },
      ],
    };
  },
};
</script>

<style scoped>
.sidebar-menu {
  width: 200px;
  min-height: 100vh;
  border-right: 1px solid var(--el-border-color);
  transition: width 0.2s;
}
.sidebar-menu.collapsed {
  width: 64px;
}
.sidebar-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 20px;
  margin-right: 6px;
}
.sidebar-icon svg {
  width: 20px !important;
  height: 20px !important;
  font-size: 20px !important;
}
</style> 