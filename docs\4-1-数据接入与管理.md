# 4.1. 数据接入与管理

## 4.1.1. 数据源管理

**1. 功能目标**
* 为系统管理员提供一个统一的界面，来配置和管理所有外部数据的来源。
* 实现对不同类型数据源的适配，将异构的数据“翻译”成系统内部可识别的标准格式。
* 确保数据连接的稳定性和可靠性，并提供有效的监控和测试手段。

**2. 目标用户**
* 主要用户：系统管理员、实施工程师。
* 次要用户：质检主管。

**3. 核心功能与界面描述**

### 3.1. 数据源列表页
* 页面布局：采用标准的“头部+筛选器+列表+分页”布局。
* 页面元素：
  * 页面标题：“数据源管理”。
  * 核心操作按钮：
    * [主要] 新建数据源：点击后以抽屉（Drawer）形式弹出创建数据源的向导式表单。
  * 搜索筛选器：
    * 数据源名称：按名称模糊搜索。
    * 数据源类型：下拉选择（如：监控目录、API接口、数据库直连）。
    * 连接状态：下拉选择（如：正常、异常、未测试）。
  * 数据源列表（Table）：
    * 列定义：
      * 序号
      * 数据源名称：用户自定义的名称，并附有代表类型的图标（如文件夹图标、API图标）。
      * 数据源类型：用标签（Tag）显示。
      * 连接状态：用带状态图标和颜色的徽章（Badge）显示，如绿色“正常”，红色“异常”。直观反映健康度。
      * 创建人
      * 创建时间
      * 操作：包含一组操作按钮：
        * [编辑]：点击后弹出与创建时类似的抽屉表单，用于修改配置。
        * [测试连接]：手动触发一次连接测试，验证配置是否正确，并实时反馈结果。
        * [删除]：删除该数据源配置（有二次确认）。
  * 分页组件：用于浏览大量数据源配置。

### 3.2. 新建/编辑数据源抽屉（分步表单）
* 这是一个向导式的配置界面，根据用户选择的数据源类型，动态展示不同的配置项。
* 第一步：选择数据源类型
  * 以图文并茂的卡片形式，提供三种选项：
    * 监控目录：用于通过SFTP/FTP等协议接入录音和元数据文件。
    * API接口：用于通过HTTP/S接口从外部系统（如CRM）拉取数据。
    * 数据库直连：用于直接连接到业务数据库进行只读查询。
* 第二步：填写连接配置（根据第一步选择动态变化）
  * 通用字段：数据源名称（必填）。
  * 当选择“监控目录”时：
    * 连接协议：下拉选择（SFTP, FTP, SMB）。
    * 服务器地址、端口。
    * 认证方式：用户名/密码 或 密钥文件。
    * 录音文件路径：指定存放录音文件的目录。
    * 元数据文件路径：指定存放通话记录（如CSV/JSON文件）的目录。
  * 当选择“API接口”时：
    * 基础URL：API服务的根地址。
    * 认证方式：下拉选择（无认证, API Key, Bearer Token, OAuth2.0），并动态显示相应的输入字段。
    * 录音文件处理策略：选项（同步拉取并存储 或 引用外部链接）。
  * 当选择“数据库直连”时：
    * 数据库类型：下拉选择（MySQL, PostgreSQL等）。
    * 服务器地址、端口、数据库名、用户名、密码。
    * SQL查询模板：一个强大的文本域，允许管理员编写自定义SQL查询。支持使用 {{last_execution_time}} 等变量来实现增量同步。
* 第三步：配置字段映射
  * 目的：将外部数据源中的字段，映射到系统内部的标准字段上。
  * 界面：一个两列表格。
    * 左列（系统标准字段）：固定显示系统所需的核心字段，如“录音唯一ID”、“通话开始时间”、“坐席工号”、“客户号码”等，并有字段说明。
    * 右列（外部字段路径/名称）：需要用户填写的输入框。支持“点分路径”语法（如 data.user.id）来处理复杂的JSON/CSV结构。
  * 预览功能：在“测试连接”成功后，系统会获取一份样本数据，并根据用户填写的映射路径，自动在右列下方填充“示例值”，让用户可以直观地验证映射配置是否正确。
* 抽屉底部操作栏：
  * 测试连接：在填写过程中随时可以点击，验证当前配置。
  * 保存 和 取消。

**4. 业务规则**
* 数据源名称在系统内必须唯一。
* 在删除一个正在被“质检计划”使用的数据源时，系统应给出强提示，并阻止删除，除非用户先修改相关计划。
* “测试连接”不仅要验证网络连通性，还应尝试获取一条样本数据并验证字段映射是否能成功解析。

**5. 成功标准**
* 管理员能够独立、顺利地完成至少一种主流数据源（如SFTP）的配置。
* 系统的后台任务能够根据配置，自动、周期性地从数据源拉取新数据。

## 4.1.2. 语音识别引擎管理

**1. 功能目标**
* 为系统管理员提供一个统一的界面，来配置和管理用于将通话录音转换为文本的语音识别（STT）引擎。
* 支持集成多种主流的云端及本地化STT服务，为企业提供灵活的选择。
* 实现STT服务与上层业务的解耦，使得更换或增加STT供应商时，无需修改核心业务代码。
* 确保系统在处理质检任务时，能够调用到可用且经过验证的STT引擎。

**2. 目标用户**
* 主要用户：系统管理员、实施工程师。
* 次要用户：质检主管。

**3. 核心功能与界面描述**

### 3.1. STT引擎列表页
* 页面布局：与数据源管理页面类似，采用标准的“头部+筛选器+列表+分页”布局，保持体验一致性。
* 页面元素：
  * 页面标题：“语音识别引擎管理”。
  * 核心操作按钮：
    * [主要] 添加引擎：点击后以抽屉（Drawer）形式弹出添加新引擎的表单。
  * 搜索筛选器：
    * 引擎名称/服务商：按自定义名称或服务商名称模糊搜索。
    * 引擎类型：下拉选择（云端引擎、离线引擎）。
    * 状态：下拉选择（运行中、未启用）。
  * 引擎列表（Table）：
    * 列定义：
      * 序号
      * 引擎名称：用户自定义的名称，便于业务人员理解。下方可能会有服务商的官方名称作为副标题。
      * 引擎类型：用带图标的徽章清晰地区分“云端引擎”（Cloud图标）和“离线引擎”（HardDrive图标）。
      * 服务商：如阿里云、腾讯云、OpenAI(Whisper)等。
      * 状态：用“运行中”或“未启用”的徽章显示。系统在处理任务时，只会调用“运行中”的引擎。
      * 最后使用时间：记录该引擎最近一次被成功调用的时间，便于判断其活跃度。
      * 操作：包含一组操作按钮：
        * [编辑]：点击后弹出抽屉表单，修改引擎配置。
        * [启用/禁用]：快速切换引擎的可用状态。这是一个开关式的操作，而非删除。
        * [删除]：永久删除该引擎配置（有二次确认和风险提示）。
  * 分页组件：用于浏览多个引擎配置。

### 3.2. 添加/编辑引擎抽屉
* 这是一个配置界面，其表单内容会根据选择的引擎类型动态变化。
* 表单字段：
  * 引擎名称：用户自定义的别名（必填）。
  * 引擎类型：下拉选择框，选项为“云端引擎”或“离线引擎”（必选）。这是核心选择，决定了后续的配置项。
  * 服务商：下拉选择框，列出系统已支持的服务商（如阿里云、腾讯云等）。
  * 当引擎类型为“云端引擎”时，显示：
    * API 端点：该云服务的接口地址（可能预置，也可能允许用户修改）。
    * API 密钥：访问云服务所需的凭证（如AccessKey ID / Secret）。为安全起见，输入框应为密码类型，并提供显示/隐藏切换。
  * 当引擎类型为“离线引擎”时，显示：
    * 模型路径/服务地址：指向部署在本地服务器上的模型文件路径或其封装的服务接口地址。
  * 描述：文本域，用于说明该引擎的特点或用途。
  * 启用状态：一个开关（Switch）控件，用于设置创建后是否立即启用。
* 抽屉底部操作栏：
  * 保存 和 取消。

**4. 业务规则**
* 引擎名称在系统内必须唯一。
* 系统必须至少有一个“运行中”的引擎，否则在尝试禁用最后一个运行时，应给出提示。
* 在删除一个引擎配置前，系统应检查是否有未完成的任务正在使用该引擎，并给出相应提示。
* 系统支持配置多个引擎，但需要一个明确的机制（如默认引擎设置，或在质检计划中指定）来决定在处理任务时优先调用哪个引擎。（此点可在后续版本中增强为负载均衡或故障转移策略）。
* API密钥等敏感信息在数据库中存储时必须加密。

**5. 成功标准**
* 管理员能够成功配置至少一种云端STT引擎，并使其处于“运行中”状态。
* 在创建质检任务后，系统的处理日志中能明确看到调用了指定的STT引擎，并成功将录音转换为了文本。
* 禁用某个引擎后，新任务不再调用该引擎。

## 4.1.3. 大语言模型管理

**1. 功能目标**
* 为系统管理员提供一个统一的界面，来集中配置和管理用于高级认知分析的大语言模型（LLM）服务。
* 支持集成主流的云端LLM API及本地化部署（如通过Ollama）的开源模型，为企业在成本、性能和数据隐私之间提供灵活选择。
* 将LLM服务的具体实现与上层应用（如自然语言创编规则、开放式问题判断）解耦，便于模型的快速迭代和切换。
* 提供必要的管理和监控功能，确保LLM服务的可用性和安全性。

**2. 目标用户**
* 主要用户：系统管理员、实施工程师、AI策略师。
* 次要用户：质检主管（可能需要了解系统可用的AI能力）。

**3. 核心功能与界面描述**

### 3.1. LLM服务列表页
* 页面布局：与前两个管理页面保持高度一致，采用“头部+筛选器+列表+分页”布局。
* 页面元素：
  * 页面标题：“大语言模型管理”。
  * 核心操作按钮：
    * [主要] 添加模型：点击后以抽屉（Drawer）形式弹出添加新LLM服务的表单。
  * 搜索筛选器：
    * 模型名称、模型类型（云端/本地）、服务商、状态（运行中/未启用/错误）。
  * 模型列表（Table）：
    * 列定义：
      * 序号
      * 模型名称：用户自定义的名称，下方是该模型的官方标识（如deepseek-chat, llama3:8b）。
      * 类型：用徽章显示“云端”或“本地”。
      * 服务商：如DeepSeek, 火山引擎, 阿里云, Ollama等。
      * API Key：安全显示。默认以部分打码形式（如sk-********）展示，旁边提供一个“眼睛”图标，点击后才显示完整密钥，以防信息泄露。
      * 状态：用带状态图标的徽章显示“运行中”、“未启用”或“错误”。“错误”状态表示最近一次调用失败。
      * 最后使用时间：记录该模型最近一次被调用的时间。
      * 操作：包含一组操作按钮：
        * [编辑]：修改模型配置。
        * [测试连接]：发送一个简单的测试请求，验证API Key和端点是否可用。
        * [启用/禁用]：快速切换模型是否在系统中可用。
        * [删除]：删除该模型配置。

### 3.2. 添加/编辑模型抽屉
* 这是一个结构化的配置界面，用于添加或修改一个LLM服务。
* 表单字段：
  * 模型名称：用户自定义的别名（必填）。
  * 模型类型：下拉选择“云端模型”或“本地模型”。
  * 服务商：根据模型类型，动态加载可选的服务商列表。
  * 模型标识：需要用户填写的、服务商要求的具体模型ID（如deepseek-chat, glm-4）。
  * API 端点：模型的服务地址。对于主流云服务商可预置，但允许修改。
  * API 密钥：访问服务的凭证。
  * 描述：对该模型用途或特点的说明。
  * 高级参数（可折叠区域）：
    * 最大 Token 数：用于限制模型的输入输出总长度，以控制成本和响应时间。
    * 温度参数 (Temperature)：控制模型输出的创造性/随机性，数值越高越有创造性。
  * 启用状态：开关（Switch），设置创建后是否立即启用。
* 抽屉底部操作栏：
  * 保存 和 取消。

**4. 业务规则**
* 模型名称在系统内必须唯一。
* API密钥等敏感信息在后端传输和存储时必须全程加密。
* “测试连接”功能应向目标模型发送一个标准化的测试prompt（如“你好”），并验证是否能收到成功的、符合预期的响应。
* 当上层应用（如规则引擎）需要调用LLM时，它会从处于“运行中”状态的模型列表中，根据指定的模型名称或默认设置进行调用。
* 如果一个模型在调用时连续多次失败，其状态应自动更新为“错误”，并可能触发系统通知给管理员。

**5. 与其他模块的关联**
* 规则库管理：在创建基于自然语言的规则或开放式问题判断规则时，会调用此处配置的LLM服务。用户在创建此类规则时，可以选择使用哪个已配置好的模型。

**6. 成功标准**
* 管理员能够成功配置一个云端或本地的LLM服务，并通过“测试连接”验证其可用性。
* 用户在“规则库管理”中，能够使用自然语言成功创建一个质检规则，后台日志显示系统正确调用了此处配置的LLM模型来完成解析。

## 4.1.4. 通知渠道管理

**1. 功能目标**
* 为系统管理员提供一个统一的、安全的技术配置界面，用于管理系统向外部发送通知的所有技术通道。
* 将通知的“如何发送”（How）与业务逻辑中的“何时发送”（When）和“发送什么”（What）进行彻底解耦。
* 支持配置多种主流的通知方式，如邮件（SMTP）和通用Webhook，以实现与企业内部各种办公系统（钉钉、企业微信、Slack、飞书、工单系统等）的灵活集成。
* 确保所有通知渠道配置的可用性和可靠性，并提供便捷的测试功能。

**2. 目标用户**
* 主要用户：系统管理员、实施工程师。

**3. 核心功能与界面描述**

### 3.1. 页面布局
* 本页面不采用传统的列表布局，而是采用卡片式布局。每个通知渠道类型（如邮件服务、Webhook端点）作为一个独立的配置卡片，内容区隔清晰，便于管理员聚焦于单项配置。
* 页面标题：“通知渠道管理”。
* 页面副标题：“配置系统用于发送邮件、Webhook等通知的外部服务通道”。

### 3.2. 模块一：邮件服务（SMTP）配置卡片
* 卡片标题：“邮件服务 (SMTP)”。
* 状态指示器：标题旁边有一个状态徽章，显示如未配置、连接正常（绿色）、连接失败（红色）。
* 表单字段 (Form Fields)：
  * SMTP 服务器地址 (Host)：输入框，例如 smtp.example.com。
  * 端口 (Port)：输入框，例如 465 或 587。
  * 加密方式 (Security)：下拉选择框，选项为 无、SSL、TLS/STARTTLS。
  * 发件人邮箱 (Sender Email)：输入框，系统发送邮件时显示的发件人地址。
  * 发件人名称 (Sender Name)：输入框（可选），邮件中显示的发件人昵称，如“智能质检系统预警中心”。
  * 认证用户名 (Username)：输入框，通常与发件人邮箱相同。
  * 认证密码/授权码 (Password/Auth Code)：密码输入框。提供帮助提示：“为增强安全性，推荐使用邮箱服务商提供的专用授权码而非登录密码。”
* 卡片底部操作 (Card Actions)：
  * [保存配置]：点击后，系统会尝试用新配置连接SMTP服务器。连接成功则保存并更新状态，失败则提示错误信息。
  * [发送测试邮件]：弹出一个小模态框，让管理员输入一个收件箱地址，点击发送后，系统会用当前已保存的配置发送一封测试邮件，用于验证端到端的可用性。

### 3.3. 模块二：Webhook 端点管理卡片
* 卡片标题：“Webhook 端点”。
* 功能说明：卡片顶部附有一行简短说明文字：“Webhook可用于将预警事件实时推送到钉钉、企业微信、Slack或任何支持Webhook的自定义系统中。”
* 核心操作：[新建 Webhook] 按钮，点击后弹出用于创建新Webhook的模态框。
* Webhook 列表 (Table)：在卡片内部，用一个表格展示所有已创建的Webhook端点。
  * 列定义：名称、URL（部分打码）、状态（启用/禁用）、操作（编辑、测试、删除）。
* 新建/编辑 Webhook 模态框：
  * 名称：必填，用于在业务规则（如预警规则）中进行选择，例如“钉钉-质检主管群机器人”。
  * URL：必填，接收数据的接口地址。
  * 请求方法 (Method)：下拉选择 POST（常用）或 GET。
  * 自定义请求头 (Headers)：一个可动态增减的键值对列表，用于添加 Content-Type、Authorization 等认证信息。
  * 请求体模板 (Body Template)：核心高级功能。
    * 控件：一个支持语法高亮的代码/文本编辑器（如 Monaco Editor）。
    * 功能：允许管理员自定义推送出去的JSON数据结构，以适配不同接收端的要求。
    * 变量支持：提供一个变量列表或帮助文档，告知管理员可以在模板中使用哪些预设变量，例如：
      * {{alert.id}} - 预警ID
      * {{alert.level}} - 预警等级
      * {{rule.name}} - 触发的规则名称
      * {{agent.name}} - 关联的坐席姓名
      * {{context.summary}} - 上下文摘要
      * {{timestamp}} - 触发时间
    * 示例：提供一个符合钉钉机器人格式的JSON模板作为默认值，方便用户快速上手。

**4. 与其他模块的关联**
* 规则库管理：在创建或编辑一个“预警规则”时，如果用户选择了“Webhook”作为通知方式，系统会提供一个下拉框，让他从本页面已配置且“已启用”的Webhook列表中选择一个或多个作为推送目标。

**5. 业务规则**
* SMTP配置是全局唯一的。
* Webhook端点可以配置多个，以支持向不同的系统或群组推送消息。
* 所有敏感信息（如密码、API Key）在后端必须加密存储。
* “测试”功能至关重要，每个配置项都应提供便捷的测试手段，以确保在生产环境中配置的正确性和可用性。

**6. 成功标准**
* 管理员能够成功配置SMTP服务，并能通过“发送测试邮件”功能收到邮件。
* 管理员能够成功配置一个指向钉钉群机器人的Webhook，并通过“测试”功能在钉钉群里收到一条格式正确的测试消息。
* 在预警规则中成功关联了通知渠道后，当规则被触发时，相应的通知能够被准确无误地发送出去。 