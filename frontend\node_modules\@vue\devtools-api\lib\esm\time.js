let supported;
let perf;
export function isPerformanceSupported() {
    var _a;
    if (supported !== undefined) {
        return supported;
    }
    if (typeof window !== 'undefined' && window.performance) {
        supported = true;
        perf = window.performance;
    }
    else if (typeof globalThis !== 'undefined' && ((_a = globalThis.perf_hooks) === null || _a === void 0 ? void 0 : _a.performance)) {
        supported = true;
        perf = globalThis.perf_hooks.performance;
    }
    else {
        supported = false;
    }
    return supported;
}
export function now() {
    return isPerformanceSupported() ? perf.now() : Date.now();
}
