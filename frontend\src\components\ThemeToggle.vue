<template>
  <el-switch
    v-model="checked"
    :active-text="'暗色'"
    :inactive-text="'亮色'"
    :active-value="true"
    :inactive-value="false"
    @change="onToggle"
    inline-prompt
    style="margin-left: 16px;"
  />
</template>

<script>
/**
 * 主题切换按钮组件
 * @module ThemeToggle
 * @prop {Boolean} isDark 当前是否暗色
 * @event toggle-theme 切换主题事件
 */
export default {
  name: 'ThemeToggle',
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      checked: this.isDark,
    };
  },
  watch: {
    isDark(val) {
      this.checked = val;
    },
  },
  methods: {
    /**
     * 切换主题
     * @param {boolean} val
     */
    onToggle(val) {
      this.$emit('toggle-theme', val);
    },
  },
};
</script> 