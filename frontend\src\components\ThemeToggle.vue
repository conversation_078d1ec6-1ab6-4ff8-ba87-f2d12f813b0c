<template>
  <div class="theme-toggle-wrapper">
    <el-switch
      v-model="checked"
      :active-icon="Moon"
      :inactive-icon="Sunny"
      :active-value="true"
      :inactive-value="false"
      @change="onToggle"
      class="theme-switch"
      size="large"
    />
  </div>
</template>

<script>
import { <PERSON>, <PERSON> } from '@element-plus/icons-vue';

/**
 * 主题切换按钮组件
 * @module ThemeToggle
 * @prop {Boolean} isDark 当前是否暗色
 * @event toggle-theme 切换主题事件
 */
export default {
  name: 'ThemeToggle',
  components: {
    Moon,
    Sunny,
  },
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      checked: this.isDark,
    };
  },
  watch: {
    isDark(val) {
      this.checked = val;
    },
  },
  methods: {
    /**
     * 切换主题
     * @param {boolean} val
     */
    onToggle(val) {
      this.$emit('toggle-theme', val);
    },
  },
};
</script>

<style scoped>
.theme-toggle-wrapper {
  display: flex;
  align-items: center;
  padding: 4px;
}

:deep(.theme-switch) {
  --el-switch-on-color: var(--gradient-primary);
  --el-switch-off-color: #ddd;
  --el-switch-border-color: var(--border-color);
}

:deep(.theme-switch .el-switch__core) {
  background: var(--bg-secondary) !important;
  border: 2px solid var(--border-color) !important;
  transition: all var(--transition-normal) !important;
  box-shadow: var(--shadow-sm);
}

:deep(.theme-switch.is-checked .el-switch__core) {
  background: var(--gradient-primary) !important;
  border-color: transparent !important;
  box-shadow: var(--shadow-md);
}

:deep(.theme-switch .el-switch__action) {
  background: white !important;
  box-shadow: var(--shadow-md) !important;
  transition: all var(--transition-normal) !important;
}

:deep(.theme-switch:hover .el-switch__core) {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

:deep(.theme-switch .el-switch__inner .el-icon) {
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

:deep(.theme-switch.is-checked .el-switch__inner .el-icon) {
  color: white;
}

/* 暗色模式适配 */
.dark :deep(.theme-switch .el-switch__core) {
  background: var(--bg-dark-secondary) !important;
  border-color: var(--border-dark) !important;
}

.dark :deep(.theme-switch .el-switch__action) {
  background: var(--bg-dark-primary) !important;
}

.dark :deep(.theme-switch .el-switch__inner .el-icon) {
  color: var(--text-dark-secondary);
}
</style>