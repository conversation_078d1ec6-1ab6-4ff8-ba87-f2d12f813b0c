<template>
  <div class="home-page">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h1 class="welcome-title gradient-text">欢迎使用智能客服质检系统</h1>
          <p class="welcome-subtitle">基于AI技术的全方位客服质量管理平台</p>
        </div>
        <div class="banner-stats">
          <div class="stat-item">
            <div class="stat-number">1,234</div>
            <div class="stat-label">今日质检任务</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">98.5%</div>
            <div class="stat-label">质检准确率</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">156</div>
            <div class="stat-label">待处理申诉</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作卡片 -->
    <div class="quick-actions">
      <h2 class="section-title">快速操作</h2>
      <div class="action-grid">
        <div class="action-card hover-lift" v-for="action in quickActions" :key="action.id">
          <div class="card-icon" :style="{ background: action.gradient }">
            <component :is="action.icon" />
          </div>
          <div class="card-content">
            <h3 class="card-title">{{ action.title }}</h3>
            <p class="card-description">{{ action.description }}</p>
          </div>
          <div class="card-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统概览 -->
    <div class="system-overview">
      <div class="overview-left">
        <h2 class="section-title">系统概览</h2>
        <div class="overview-cards">
          <div class="overview-card" v-for="item in overviewData" :key="item.id">
            <div class="overview-icon" :style="{ color: item.color }">
              <component :is="item.icon" />
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ item.value }}</div>
              <div class="overview-label">{{ item.label }}</div>
              <div class="overview-trend" :class="item.trend">
                <el-icon><ArrowUp v-if="item.trend === 'up'" /><ArrowDown v-else /></el-icon>
                {{ item.change }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="overview-right">
        <div class="feature-highlight">
          <h3>核心功能特色</h3>
          <ul class="feature-list">
            <li v-for="feature in features" :key="feature.id">
              <el-icon class="feature-icon"><Check /></el-icon>
              {{ feature.text }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  ArrowRight, 
  ArrowUp, 
  ArrowDown, 
  Check,
  Setting,
  Monitor,
  DataAnalysis,
  Bell,
  User,
  Document
} from '@element-plus/icons-vue';

/**
 * 首页组件
 * @module HomePage
 */
export default {
  name: 'HomePage',
  components: {
    ArrowRight,
    ArrowUp,
    ArrowDown,
    Check,
    Setting,
    Monitor,
    DataAnalysis,
    Bell,
    User,
    Document
  },
  data() {
    return {
      quickActions: [
        {
          id: 1,
          title: '质检任务管理',
          description: '创建和管理质检任务',
          icon: 'Setting',
          gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        },
        {
          id: 2,
          title: '实时监控',
          description: '查看实时质检状态',
          icon: 'Monitor',
          gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
        },
        {
          id: 3,
          title: '数据分析',
          description: '查看质检数据报告',
          icon: 'DataAnalysis',
          gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        },
        {
          id: 4,
          title: '预警中心',
          description: '处理质检预警信息',
          icon: 'Bell',
          gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
        }
      ],
      overviewData: [
        {
          id: 1,
          label: '总质检次数',
          value: '12,345',
          change: '+12.5%',
          trend: 'up',
          color: '#409EFF',
          icon: 'Document'
        },
        {
          id: 2,
          label: '活跃用户',
          value: '1,234',
          change: '+8.2%',
          trend: 'up',
          color: '#67C23A',
          icon: 'User'
        },
        {
          id: 3,
          label: '系统可用性',
          value: '99.9%',
          change: '+0.1%',
          trend: 'up',
          color: '#E6A23C',
          icon: 'Monitor'
        }
      ],
      features: [
        { id: 1, text: 'AI智能质检，准确率高达98%+' },
        { id: 2, text: '实时监控预警，及时发现问题' },
        { id: 3, text: '多维度数据分析，深度洞察' },
        { id: 4, text: '灵活的规则配置，适应业务需求' },
        { id: 5, text: '完善的申诉复核机制' }
      ]
    };
  }
};
</script>

<style scoped>
.home-page {
  padding: 0;
  max-width: 1200px;
  margin: 0 auto;
}

/* 欢迎横幅 */
.welcome-banner {
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  padding: 40px;
  margin-bottom: 32px;
  color: white;
  position: relative;
  overflow: hidden;
}

.welcome-banner::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
  background: linear-gradient(45deg, #fff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.banner-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 快速操作 */
.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: var(--text-primary);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.action-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--text-primary);
}

.card-description {
  font-size: 14px;
  margin: 0;
  color: var(--text-secondary);
}

.card-arrow {
  color: var(--text-tertiary);
  transition: all var(--transition-fast);
}

.action-card:hover .card-arrow {
  color: var(--primary-color);
  transform: translateX(4px);
}

/* 系统概览 */
.system-overview {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
}

.overview-cards {
  display: grid;
  gap: 16px;
}

.overview-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.overview-icon {
  font-size: 24px;
}

.overview-info {
  flex: 1;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
}

.overview-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 4px 0;
}

.overview-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.overview-trend.up {
  color: #67C23A;
}

.overview-trend.down {
  color: #F56C6C;
}

/* 功能特色 */
.feature-highlight {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 24px;
  height: fit-content;
}

.feature-highlight h3 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.feature-icon {
  color: #67C23A;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .banner-stats {
    gap: 20px;
  }
  
  .system-overview {
    grid-template-columns: 1fr;
  }
  
  .action-grid {
    grid-template-columns: 1fr;
  }
}
</style>
