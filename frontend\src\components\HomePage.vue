<template>
  <div class="home-page">
    <!-- 系统介绍横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h1 class="welcome-title gradient-text">智能客服质检系统</h1>
          <p class="welcome-subtitle">以AI驱动、数据赋能为核心的全流程、闭环式服务质量管理平台</p>
          <div class="welcome-description">
            <p>重塑客服质检工作范式，实现100%全量自动化质检，构建实时风险监控与主动干预能力</p>
          </div>
        </div>
        <div class="banner-features">
          <div class="feature-item">
            <div class="feature-icon">🤖</div>
            <div class="feature-text">AI驱动</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <div class="feature-text">数据决策</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔄</div>
            <div class="feature-text">闭环管理</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">👥</div>
            <div class="feature-text">多角色协同</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心价值主张 -->
    <div class="value-propositions">
      <h2 class="section-title">核心价值主张</h2>
      <div class="value-grid">
        <div class="value-card hover-lift" v-for="value in valuePropositions" :key="value.id">
          <div class="value-icon" :style="{ background: value.gradient }">
            <component :is="value.icon" />
          </div>
          <div class="value-content">
            <h3 class="value-title">{{ value.title }}</h3>
            <p class="value-description">{{ value.description }}</p>
            <ul class="value-points">
              <li v-for="point in value.points" :key="point">{{ point }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户角色与场景 -->
    <div class="user-roles">
      <h2 class="section-title">服务角色</h2>
      <div class="roles-grid">
        <div class="role-card hover-lift" v-for="role in userRoles" :key="role.id">
          <div class="role-header">
            <div class="role-avatar" :style="{ background: role.color }">
              <component :is="role.icon" />
            </div>
            <div class="role-info">
              <h3 class="role-title">{{ role.title }}</h3>
              <p class="role-subtitle">{{ role.subtitle }}</p>
            </div>
          </div>
          <div class="role-features">
            <div class="feature-item" v-for="feature in role.features" :key="feature">
              <el-icon class="feature-check"><Check /></el-icon>
              <span>{{ feature }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品架构 -->
    <div class="product-architecture">
      <h2 class="section-title">产品架构</h2>
      <div class="architecture-content">
        <div class="architecture-description">
          <p>系统采用模块化设计，按照逻辑功能块划分为五大核心模块，实现从日常操作到决策支持的完整工作流。</p>
        </div>
        <div class="architecture-modules">
          <div class="module-item" v-for="module in architectureModules" :key="module.id">
            <div class="module-icon" :style="{ background: module.gradient }">
              <component :is="module.icon" />
            </div>
            <div class="module-content">
              <h4 class="module-title">{{ module.title }}</h4>
              <p class="module-description">{{ module.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  Check,
  Setting,
  Monitor,
  DataAnalysis,
  Bell,
  User,
  Document,
  HomeFilled,
  UserFilled,
  Tools,
  TrendCharts,
  Connection
} from '@element-plus/icons-vue';

/**
 * 首页组件
 * @module HomePage
 */
export default {
  name: 'HomePage',
  components: {
    Check,
    Setting,
    Monitor,
    DataAnalysis,
    Bell,
    User,
    Document,
    HomeFilled,
    UserFilled,
    Tools,
    TrendCharts,
    Connection
  },
  data() {
    return {
      valuePropositions: [
        {
          id: 1,
          title: '全流程闭环管理',
          description: '端到端的质量管理闭环，完美践行PDCA管理循环理念',
          icon: 'Setting',
          gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          points: [
            'Plan - 精细化定义质检标准',
            'Do - 自动化执行质检任务',
            'Check - 多维评估与验证',
            'Act - 数据驱动的持续改进'
          ]
        },
        {
          id: 2,
          title: '多角色高效协同',
          description: '为客服中心生态中所有核心角色量身打造的协同工作平台',
          icon: 'UserFilled',
          gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          points: [
            '质检主管 - 战略驾驶舱与体系设计器',
            '班组长 - 精准的团队雷达与辅导工具',
            '复核员 - 高效的任务处理器与AI校准器',
            '客服坐席 - 透明的绩效镜子与成长助手'
          ]
        },
        {
          id: 3,
          title: 'AI驱动与数据决策',
          description: '从经验直觉向AI驱动、数据决策的科学化管理模式转变',
          icon: 'DataAnalysis',
          gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
          points: [
            '100%全量自动化质检',
            '实时风险监控与主动干预',
            '非结构化信息转化为结构化智能',
            '让每一项决策都有据可依'
          ]
        }
      ],
      userRoles: [
        {
          id: 1,
          title: '质检主管',
          subtitle: '总设计师、管理者和最终决策者',
          icon: 'User',
          color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          features: [
            '设计与维护质检体系',
            '监控与管理运营流程',
            '分析与驱动决策',
            '申诉终审与体系优化'
          ]
        },
        {
          id: 2,
          title: '班组长',
          subtitle: '一线指挥官和团队辅导者',
          icon: 'UserFilled',
          color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          features: [
            '监控团队状态与绩效',
            '进行数据驱动的辅导',
            '参与一线风险管理',
            '协调申诉处理'
          ]
        },
        {
          id: 3,
          title: '复核员',
          subtitle: '质量保障者和AI校准者',
          icon: 'Bell',
          color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
          features: [
            '高效处理复核任务',
            '执行精准的复核操作',
            '参与自我校准与提升',
            '维护质检结果准确性'
          ]
        },
        {
          id: 4,
          title: '客服坐席',
          subtitle: '服务质量的直接执行者',
          icon: 'HomeFilled',
          color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
          features: [
            '了解个人绩效表现',
            '分析与学习改进',
            '对结果进行反馈与申诉',
            '持续提升服务能力'
          ]
        }
      ],
      architectureModules: [
        {
          id: 1,
          title: '概览与工作台',
          description: '高频入口与日常操作，提供角色驱动的个性化工作空间',
          icon: 'HomeFilled',
          gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        },
        {
          id: 2,
          title: '质检管理',
          description: '核心配置与标准定义，构建质检体系的规则引擎',
          icon: 'Setting',
          gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
        },
        {
          id: 3,
          title: '实时监控与干预',
          description: '风险管控，实现事中预警和主动干预能力',
          icon: 'Monitor',
          gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        },
        {
          id: 4,
          title: '数据洞察与分析',
          description: '决策支持，提供多维度分析和商业智能',
          icon: 'TrendCharts',
          gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
        },
        {
          id: 5,
          title: '系统管理',
          description: '底层支撑与技术配置，管理AI引擎和数据接入',
          icon: 'Tools',
          gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
        }
      ]
    };
  }
};
</script>

<style scoped>
.home-page {
  padding: 0;
  max-width: 1200px;
  margin: 0 auto;
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为各个部分添加延迟动画 */
.welcome-banner {
  animation: fadeInUp 0.8s ease-out 0.1s both;
}

.value-propositions {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.user-roles {
  animation: fadeInUp 0.8s ease-out 0.3s both;
}

.product-architecture {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

/* 欢迎横幅 */
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: var(--radius-xl);
  padding: 50px;
  margin-bottom: 40px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
}

.welcome-banner::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%);
  animation: float 8s ease-in-out infinite;
}

.welcome-banner::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-30px) rotate(120deg) scale(1.1);
    opacity: 0.6;
  }
  66% {
    transform: translateY(-15px) rotate(240deg) scale(0.9);
    opacity: 0.4;
  }
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.welcome-title {
  font-size: 36px;
  font-weight: 800;
  margin: 0 0 20px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% { filter: drop-shadow(2px 2px 8px rgba(0, 0, 0, 0.3)); }
  100% { filter: drop-shadow(2px 2px 16px rgba(255, 255, 255, 0.5)); }
}

.welcome-subtitle {
  font-size: 20px;
  margin: 0 0 24px 0;
  opacity: 0.95;
  font-weight: 500;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.welcome-description {
  margin-top: 0;
  font-size: 17px;
  opacity: 0.9;
  line-height: 1.7;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.banner-features {
  display: flex;
  gap: 28px;
  margin-top: 32px;
  justify-content: center;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.feature-item:hover::before {
  left: 100%;
}

.feature-item:hover {
  transform: translateY(-8px) scale(1.05);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.feature-icon {
  font-size: 32px;
  opacity: 0.95;
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.feature-item:hover .feature-icon {
  transform: scale(1.2) rotate(10deg);
  opacity: 1;
}

.feature-text {
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  opacity: 0.95;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  line-height: 1.3;
  word-wrap: break-word;
  white-space: normal;
  max-width: 80px;
}

.feature-item:hover .feature-text {
  opacity: 1;
  transform: translateY(-2px);
}

/* 核心价值主张 */
.section-title {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 40px 0;
  color: var(--text-primary);
  text-align: center;
  position: relative;
  background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 2px;
  animation: titleUnderline 2s ease-in-out infinite alternate;
}

@keyframes titleUnderline {
  0% { width: 60px; opacity: 0.8; }
  100% { width: 100px; opacity: 1; }
}

.value-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
  margin-bottom: 64px;
}

.value-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 36px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.value-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.value-card:hover::before {
  left: 100%;
}

.value-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
  border-color: var(--primary-color);
}

.value-card:nth-child(odd):hover {
  transform: translateY(-12px) scale(1.02) rotate(2deg);
}

.value-card:nth-child(even):hover {
  transform: translateY(-12px) scale(1.02) rotate(-2deg);
}

.value-icon {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  margin-bottom: 24px;
}

.value-content {
  flex: 1;
}

.value-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: var(--text-primary);
}

.value-description {
  font-size: 16px;
  margin: 0 0 20px 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.value-points {
  list-style: none;
  padding: 0;
  margin: 0;
}

.value-points li {
  padding: 8px 0;
  color: var(--text-secondary);
  font-size: 14px;
  position: relative;
  padding-left: 20px;
}

.value-points li::before {
  content: '•';
  color: var(--primary-color);
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* 用户角色 */
.user-roles {
  margin-bottom: 64px;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.role-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 28px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.role-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.role-card:hover::before {
  left: 100%;
}

.role-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.role-card:nth-child(odd):hover {
  transform: translateY(-8px) scale(1.02) rotate(1deg);
}

.role-card:nth-child(even):hover {
  transform: translateY(-8px) scale(1.02) rotate(-1deg);
}

.role-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.role-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.role-avatar::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.role-card:hover .role-avatar {
  transform: scale(1.15) rotate(10deg);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
}

.role-card:hover .role-avatar::after {
  opacity: 1;
}

.role-info {
  flex: 1;
}

.role-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 6px 0;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.role-subtitle {
  font-size: 15px;
  margin: 0;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.role-card:hover .role-subtitle {
  color: var(--text-primary);
}

.role-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.role-features .feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: rgba(var(--primary-color-rgb), 0.05);
  border: 1px solid rgba(var(--primary-color-rgb), 0.1);
  border-radius: var(--radius-md);
  backdrop-filter: blur(5px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 32px;
}

.role-features .feature-item:hover {
  transform: scale(1.05);
  background: rgba(var(--primary-color-rgb), 0.1);
  border-color: rgba(var(--primary-color-rgb), 0.2);
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.15);
}

.feature-check {
  color: #67C23A;
  font-size: 14px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.role-features .feature-item:hover .feature-check {
  transform: scale(1.2);
  color: #52c41a;
}

.role-features .feature-item span {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
  transition: color 0.3s ease;
  line-height: 1.2;
}

.role-features .feature-item:hover span {
  color: var(--text-primary);
}

/* 产品架构 */
.product-architecture {
  margin-bottom: 64px;
}

.architecture-content {
  max-width: 1000px;
  margin: 0 auto;
}

.architecture-description {
  text-align: center;
  margin-bottom: 40px;
}

.architecture-description p {
  font-size: 16px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.architecture-modules {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 第一行：3个模块，每个占2列 */
.architecture-modules .module-item:nth-child(1) {
  grid-column: 1 / 3;
}

.architecture-modules .module-item:nth-child(2) {
  grid-column: 3 / 5;
}

.architecture-modules .module-item:nth-child(3) {
  grid-column: 5 / 7;
}

/* 第二行：2个模块居中，每个占2列，中间留1列空隙 */
.architecture-modules .module-item:nth-child(4) {
  grid-column: 2 / 4;
}

.architecture-modules .module-item:nth-child(5) {
  grid-column: 4 / 6;
}

.module-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 28px;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.module-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.module-item:hover::before {
  left: 100%;
}

.module-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.module-item:nth-child(odd):hover {
  transform: translateY(-8px) scale(1.02) rotate(1deg);
}

.module-item:nth-child(even):hover {
  transform: translateY(-8px) scale(1.02) rotate(-1deg);
}

.module-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.module-icon::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 18px;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.module-item:hover .module-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
}

.module-item:hover .module-icon::after {
  opacity: 1;
}

.module-content {
  flex: 1;
  padding-top: 4px;
}

.module-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: var(--text-primary);
  transition: color 0.3s ease;
  background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.module-description {
  font-size: 15px;
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.6;
  transition: color 0.3s ease;
}

.module-item:hover .module-description {
  color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .banner-features {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .value-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .roles-grid {
    grid-template-columns: 1fr;
  }

  .architecture-modules {
    grid-template-columns: 1fr;
  }

  /* 移动端重置特殊布局 */
  .architecture-modules {
    grid-template-columns: 1fr !important;
  }

  .architecture-modules .module-item:nth-child(1),
  .architecture-modules .module-item:nth-child(2),
  .architecture-modules .module-item:nth-child(3),
  .architecture-modules .module-item:nth-child(4),
  .architecture-modules .module-item:nth-child(5) {
    grid-column: auto !important;
  }

  /* 移动端功能列表单列显示 */
  .role-features {
    grid-template-columns: 1fr !important;
  }

  .section-title {
    font-size: 20px;
  }

  .welcome-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .banner-features {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 12px;
  }

  .value-card,
  .role-card,
  .module-item {
    padding: 20px;
  }
}
</style>
