<template>
  <div class="home-page">
    <!-- 系统介绍横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h1 class="welcome-title gradient-text">智能客服质检系统</h1>
          <p class="welcome-subtitle">以AI驱动、数据赋能为核心的全流程、闭环式服务质量管理平台</p>
          <div class="welcome-description">
            <p>重塑客服质检工作范式，实现100%全量自动化质检，构建实时风险监控与主动干预能力</p>
          </div>
        </div>
        <div class="banner-features">
          <div class="feature-item">
            <div class="feature-icon">🤖</div>
            <div class="feature-text">AI驱动</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <div class="feature-text">数据决策</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔄</div>
            <div class="feature-text">闭环管理</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">👥</div>
            <div class="feature-text">多角色协同</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心价值主张 -->
    <div class="value-propositions">
      <h2 class="section-title">核心价值主张</h2>
      <div class="value-grid">
        <div class="value-card hover-lift" v-for="value in valuePropositions" :key="value.id">
          <div class="value-icon" :style="{ background: value.gradient }">
            <component :is="value.icon" />
          </div>
          <div class="value-content">
            <h3 class="value-title">{{ value.title }}</h3>
            <p class="value-description">{{ value.description }}</p>
            <ul class="value-points">
              <li v-for="point in value.points" :key="point">{{ point }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户角色与场景 -->
    <div class="user-roles">
      <h2 class="section-title">服务角色</h2>
      <div class="roles-grid">
        <div class="role-card hover-lift" v-for="role in userRoles" :key="role.id">
          <div class="role-header">
            <div class="role-avatar" :style="{ background: role.color }">
              <component :is="role.icon" />
            </div>
            <div class="role-info">
              <h3 class="role-title">{{ role.title }}</h3>
              <p class="role-subtitle">{{ role.subtitle }}</p>
            </div>
          </div>
          <div class="role-features">
            <div class="feature-item" v-for="feature in role.features" :key="feature">
              <el-icon class="feature-check"><Check /></el-icon>
              <span>{{ feature }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品架构 -->
    <div class="product-architecture">
      <h2 class="section-title">产品架构</h2>
      <div class="architecture-content">
        <div class="architecture-description">
          <p>系统采用模块化设计，按照逻辑功能块划分为五大核心模块，实现从日常操作到决策支持的完整工作流。</p>
        </div>
        <div class="architecture-modules">
          <div class="module-item" v-for="module in architectureModules" :key="module.id">
            <div class="module-icon" :style="{ background: module.gradient }">
              <component :is="module.icon" />
            </div>
            <div class="module-content">
              <h4 class="module-title">{{ module.title }}</h4>
              <p class="module-description">{{ module.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  Check,
  Setting,
  Monitor,
  DataAnalysis,
  Bell,
  User,
  Document,
  HomeFilled,
  UserFilled,
  Tools,
  TrendCharts,
  Connection
} from '@element-plus/icons-vue';

/**
 * 首页组件
 * @module HomePage
 */
export default {
  name: 'HomePage',
  components: {
    Check,
    Setting,
    Monitor,
    DataAnalysis,
    Bell,
    User,
    Document,
    HomeFilled,
    UserFilled,
    Tools,
    TrendCharts,
    Connection
  },
  data() {
    return {
      valuePropositions: [
        {
          id: 1,
          title: '全流程闭环管理',
          description: '端到端的质量管理闭环，完美践行PDCA管理循环理念',
          icon: 'Setting',
          gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          points: [
            'Plan - 精细化定义质检标准',
            'Do - 自动化执行质检任务',
            'Check - 多维评估与验证',
            'Act - 数据驱动的持续改进'
          ]
        },
        {
          id: 2,
          title: '多角色高效协同',
          description: '为客服中心生态中所有核心角色量身打造的协同工作平台',
          icon: 'UserFilled',
          gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          points: [
            '质检主管 - 战略驾驶舱与体系设计器',
            '班组长 - 精准的团队雷达与辅导工具',
            '复核员 - 高效的任务处理器与AI校准器',
            '客服坐席 - 透明的绩效镜子与成长助手'
          ]
        },
        {
          id: 3,
          title: 'AI驱动与数据决策',
          description: '从经验直觉向AI驱动、数据决策的科学化管理模式转变',
          icon: 'DataAnalysis',
          gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
          points: [
            '100%全量自动化质检',
            '实时风险监控与主动干预',
            '非结构化信息转化为结构化智能',
            '让每一项决策都有据可依'
          ]
        }
      ],
      userRoles: [
        {
          id: 1,
          title: '质检主管',
          subtitle: '总设计师、管理者和最终决策者',
          icon: 'User',
          color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          features: [
            '设计与维护质检体系',
            '监控与管理运营流程',
            '分析与驱动决策',
            '申诉终审与体系优化'
          ]
        },
        {
          id: 2,
          title: '班组长',
          subtitle: '一线指挥官和团队辅导者',
          icon: 'UserFilled',
          color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          features: [
            '监控团队状态与绩效',
            '进行数据驱动的辅导',
            '参与一线风险管理',
            '协调申诉处理'
          ]
        },
        {
          id: 3,
          title: '复核员',
          subtitle: '质量保障者和AI校准者',
          icon: 'Bell',
          color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
          features: [
            '高效处理复核任务',
            '执行精准的复核操作',
            '参与自我校准与提升',
            '维护质检结果准确性'
          ]
        },
        {
          id: 4,
          title: '客服坐席',
          subtitle: '服务质量的直接执行者',
          icon: 'HomeFilled',
          color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
          features: [
            '了解个人绩效表现',
            '分析与学习改进',
            '对结果进行反馈与申诉',
            '持续提升服务能力'
          ]
        }
      ],
      architectureModules: [
        {
          id: 1,
          title: '概览与工作台',
          description: '高频入口与日常操作，提供角色驱动的个性化工作空间',
          icon: 'HomeFilled',
          gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        },
        {
          id: 2,
          title: '质检管理',
          description: '核心配置与标准定义，构建质检体系的规则引擎',
          icon: 'Setting',
          gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
        },
        {
          id: 3,
          title: '实时监控与干预',
          description: '风险管控，实现事中预警和主动干预能力',
          icon: 'Monitor',
          gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        },
        {
          id: 4,
          title: '数据洞察与分析',
          description: '决策支持，提供多维度分析和商业智能',
          icon: 'TrendCharts',
          gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
        },
        {
          id: 5,
          title: '系统管理',
          description: '底层支撑与技术配置，管理AI引擎和数据接入',
          icon: 'Tools',
          gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
        }
      ]
    };
  }
};
</script>

<style scoped>
.home-page {
  padding: 0;
  max-width: 1200px;
  margin: 0 auto;
}

/* 欢迎横幅 */
.welcome-banner {
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  padding: 40px;
  margin-bottom: 32px;
  color: white;
  position: relative;
  overflow: hidden;
}

.welcome-banner::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
  background: linear-gradient(45deg, #fff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.welcome-description {
  margin-top: 16px;
  font-size: 16px;
  opacity: 0.9;
  line-height: 1.6;
}

.banner-features {
  display: flex;
  gap: 24px;
  margin-top: 20px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-fast);
}

.feature-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.15);
}

.feature-icon {
  font-size: 24px;
}

.feature-text {
  font-size: 14px;
  font-weight: 600;
  text-align: center;
}

/* 核心价值主张 */
.section-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 32px 0;
  color: var(--text-primary);
  text-align: center;
}

.value-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
  margin-bottom: 64px;
}

.value-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 32px;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.value-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.value-icon {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  margin-bottom: 24px;
}

.value-content {
  flex: 1;
}

.value-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: var(--text-primary);
}

.value-description {
  font-size: 16px;
  margin: 0 0 20px 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.value-points {
  list-style: none;
  padding: 0;
  margin: 0;
}

.value-points li {
  padding: 8px 0;
  color: var(--text-secondary);
  font-size: 14px;
  position: relative;
  padding-left: 20px;
}

.value-points li::before {
  content: '•';
  color: var(--primary-color);
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* 用户角色 */
.user-roles {
  margin-bottom: 64px;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.role-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 24px;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.role-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.role-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.role-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.role-info {
  flex: 1;
}

.role-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: var(--text-primary);
}

.role-subtitle {
  font-size: 14px;
  margin: 0;
  color: var(--text-secondary);
}

.role-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.role-features .feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0;
  background: none;
  border: none;
  border-radius: 0;
  backdrop-filter: none;
}

.role-features .feature-item:hover {
  transform: none;
  background: none;
}

.feature-check {
  color: #67C23A;
  font-size: 16px;
  flex-shrink: 0;
}

.role-features .feature-item span {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 产品架构 */
.product-architecture {
  margin-bottom: 64px;
}

.architecture-content {
  max-width: 1000px;
  margin: 0 auto;
}

.architecture-description {
  text-align: center;
  margin-bottom: 40px;
}

.architecture-description p {
  font-size: 16px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.architecture-modules {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

/* 第一行放3个，第二行放2个并居中 */
.architecture-modules .module-item:nth-child(4) {
  grid-column: 1 / 2;
}

.architecture-modules .module-item:nth-child(5) {
  grid-column: 2 / 3;
}

.module-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 24px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.module-item:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.module-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.module-content {
  flex: 1;
}

.module-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.module-description {
  font-size: 14px;
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .banner-features {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .value-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .roles-grid {
    grid-template-columns: 1fr;
  }

  .architecture-modules {
    grid-template-columns: 1fr;
  }

  /* 移动端重置特殊布局 */
  .architecture-modules .module-item:nth-child(4),
  .architecture-modules .module-item:nth-child(5) {
    grid-column: auto;
  }

  .section-title {
    font-size: 20px;
  }

  .welcome-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .banner-features {
    grid-template-columns: 1fr;
  }

  .feature-item {
    padding: 12px;
  }

  .value-card,
  .role-card,
  .module-item {
    padding: 20px;
  }
}
</style>
